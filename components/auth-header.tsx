import Link from "next/link";
import Image from "next/image";

export function AuthHeader() {
  return (
    <header className="absolute top-0 left-0 w-full z-10 bg-white/90 backdrop-blur-xs border-b border-gray-200/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 sm:h-20">
          {/* Logo - clickable to go home */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-all duration-200 hover:scale-105">
              <Image
                src="/paw-hub-logo.png"
                alt="Paw Hub Logo"
                width={70}
                height={43}
                className="sm:w-[80px] sm:h-[49px] drop-shadow-xs"
              />
              <span className="sr-only">
                Paw Hub - Go to Home
              </span>
            </Link>
          </div>

          {/* Back to Home text link */}
          <div className="flex items-center">
            <Link
              href="/"
              className="text-xs sm:text-sm text-gray-700 hover:text-blue-600 transition-colors font-medium bg-white/80 backdrop-blur-xs px-2 py-1 sm:px-3 sm:py-1 rounded-full shadow-xs border border-gray-200/50 hover:border-blue-200 hover:shadow-md"
            >
              <span className="hidden sm:inline">← Back to Home</span>
              <span className="sm:hidden">← Home</span>
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
}
