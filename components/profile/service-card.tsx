"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";

interface ServiceCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  color: string;
}

export function ServiceCard({ icon: Icon, title, description, color }: ServiceCardProps) {
  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <CardContent className="p-6 flex flex-col h-full">
        <div className="flex flex-col items-center text-center space-y-4 flex-1">
          <div className={`p-3 rounded-full ${color}`}>
            <Icon className="w-8 h-8" />
          </div>
          
          <h3 className="font-bold text-lg text-gray-800 leading-tight">
            {title}
          </h3>
          
          <p className="text-sm text-gray-600 flex-1">
            {description}
          </p>
          
          <Button 
            className="w-full bg-yellow-400 hover:bg-yellow-500 text-white font-semibold rounded-full"
            size="sm"
          >
            →
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
