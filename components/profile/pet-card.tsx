"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Pet } from "@/types/pet";

interface PetCardProps {
  pet: Pet;
}

export function PetCard({ pet }: PetCardProps) {
  return (
    <Card className="relative overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="flex flex-col items-center text-center space-y-3">
          <Avatar className="w-16 h-16">
            <AvatarImage src={pet.image} alt={pet.name} />
            <AvatarFallback className="bg-orange-100 text-orange-600">
              {pet.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          
          <div className="space-y-1">
            <h3 className="font-bold text-sm text-gray-800 leading-tight">
              {pet.name}
            </h3>
            <p className="text-xs text-gray-500">{pet.breed}</p>
            <p className="text-xs text-gray-500">{pet.age}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
