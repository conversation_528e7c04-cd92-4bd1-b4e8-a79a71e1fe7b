"use client";

import { User } from "@supabase/supabase-js";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Heart,
  Stethoscope,
  Syringe,
  Bell,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Edit
} from "lucide-react";
import { useState } from "react";
import { PetCard } from "./pet-card"
import { ServiceCard } from "./service-card";
import { AddPetDialog } from "./add-pet-dialog";
import { Pet } from "@/types/pet";

interface ProfileContentProps {
  user: User;
}

export function ProfileContent({ user }: ProfileContentProps) {
  const [pets, setPets] = useState([
    {
      id: "1",
      name: "ALEXANDER MC MUA",
      type: "Dog",
      breed: "Golden Retriever",
      age: "2 years",
      gender: "male",
      weight: "25kg",
      color: "Golden",
      description: "Friendly and energetic dog",
      image: "/api/placeholder/80/80",
      isOwned: true
    }
  ]);

  const handleAddPet = (newPet: Pet) => {
    setPets(prev => [...prev, newPet]);
  };

  const services = [
    {
      icon: Heart,
      title: "Thêm hồ sơ thú cưng",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      color: "bg-green-100 text-green-600"
    },
    {
      icon: Stethoscope,
      title: "Kiểm tra sức khỏe với AI DOCTOR",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      color: "bg-blue-100 text-blue-600"
    },
    {
      icon: Syringe,
      title: "Nhắc lịch tiêm phòng",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      color: "bg-yellow-100 text-yellow-600"
    },
    {
      icon: Bell,
      title: "Báo cáo sức khỏe tổng quát",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      color: "bg-purple-100 text-purple-600"
    }
  ];

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  const getDisplayName = (email: string) => {
    const username = email.split('@')[0];
    return username.charAt(0).toUpperCase() + username.slice(1);
  };

  return (
    <div className="max-w-6xl mx-auto p-4 sm:p-6 lg:p-8">
      {/* Header Section */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <Avatar className="w-24 h-24 sm:w-32 sm:h-32">
            <AvatarImage src={user.user_metadata?.avatar_url} />
            <AvatarFallback className="text-2xl sm:text-3xl bg-gray-200">
              {getInitials(user.email || "")}
            </AvatarFallback>
          </Avatar>
        </div>
        
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">
          Xin chàoooo {getDisplayName(user.email || "")},
        </h1>
        
        <p className="text-gray-600 text-sm sm:text-base max-w-2xl mx-auto">
          Khám phá ngay hàng loạt tính năng từ PawHub giúp bạn dễ dàng quản lý, 
          theo dõi sức khỏe thú cưng và trang bị cho bé những tiện nghi phù hợp nhất.
        </p>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-12">
        {services.map((service, index) => (
          <ServiceCard key={index} {...service} />
        ))}
      </div>

      {/* Pets Section */}
      <div className="mb-12">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-0">
            Hình như ở đây hơi trống trải,<br />
            bạn có muốn các bé cưng của mình xuất hiện tại đây không?
          </h2>
        </div>
        
        <p className="text-gray-600 text-center mb-8">
          Thêm thú cưng của bạn ngay!
        </p>

        {/* Pets Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {pets.map((pet) => (
            <PetCard key={pet.id} pet={pet} />
          ))}

          <AddPetDialog onAddPet={handleAddPet} />
        </div>
      </div>

      {/* User Info Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold">Thông tin cá nhân</CardTitle>
            <Button variant="outline" size="sm">
              <Edit className="w-4 h-4 mr-2" />
              Chỉnh sửa
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <Mail className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p className="font-medium">{user.email}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Calendar className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Ngày tham gia</p>
                <p className="font-medium">
                  {new Date(user.created_at).toLocaleDateString('vi-VN')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Phone className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Số điện thoại</p>
                <p className="font-medium text-gray-400">Chưa cập nhật</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <MapPin className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Địa chỉ</p>
                <p className="font-medium text-gray-400">Chưa cập nhật</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
