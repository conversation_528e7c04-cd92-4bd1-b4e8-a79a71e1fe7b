"use client";

import Image from "next/image";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function AiDoctorPage() {
  return (
    <main className="relative">
      {/* Decorative paws */}
      <Image
        src="/ai-doctor/paws-grey.png"
        alt="decorative paws"
        width={260}
        height={260}
        className="pointer-events-none select-none fixed -bottom-8 -left-6 hidden md:block opacity-60"
        priority
      />
      <Image
        src="/ai-doctor/paws-grey.png"
        alt="decorative paws"
        width={180}
        height={180}
        className="pointer-events-none select-none fixed top-24 -right-8 hidden md:block rotate-180 opacity-50"
        priority
      />

      <section className="mx-auto w-full max-w-4xl px-6 py-10 md:py-14">
        {/* Page header */}
        <div className="relative mb-8 text-center md:mb-10">
          <h1 className="text-3xl font-bold tracking-tight md:text-4xl">AI Doctor</h1>
          <p className="mt-2 text-sm text-muted-foreground md:text-base">
            <PERSON><PERSON><PERSON> sĩ thú y AI thông minh - T<PERSON> vấn 24/7 cho thú c<PERSON>ng của bạn
          </p>

          <div className="pointer-events-none absolute -top-2 right-0 hidden md:block">
            <Image
              src="/ai-doctor/stupid-dog.png"
              alt="pet mascot"
              width={60}
              height={60}
              className="h-14 w-14"
              priority
            />
          </div>
        </div>

        {/* Chat surface */}
        <div className="relative rounded-2xl border bg-card p-3 shadow-xs md:p-6">
          {/* Intro from assistant */}
          <div className="rounded-xl border bg-muted/60 p-4 text-sm md:text-base">
            <div className="mb-3 flex items-center gap-2 text-muted-foreground">
              <div className="flex h-7 w-7 items-center justify-center rounded-full bg-muted">
                <span className="text-xs">👤</span>
              </div>
              <span className="text-xs">AI Doctor</span>
            </div>
            <div className="space-y-2 leading-relaxed">
              <p>
                <strong>Chào mừng bạn đến với PetCare AI Doctor!</strong> Tôi là bác sĩ thú y
                AI, chuyên gia về sức khỏe thú cưng tại Việt Nam. Tôi có thể:
              </p>
              <p>✅ <strong>Chẩn đoán sức khỏe</strong> qua triệu chứng và hình ảnh</p>
              <p>✅ <strong>Tư vấn chăm sóc</strong> phù hợp với điều kiện Việt Nam</p>
              <p>✅ <strong>Đề xuất điều trị</strong> và thuốc an toàn</p>
              <p>✅ <strong>Gợi ý bác sĩ</strong> và phòng khám gần bạn</p>
              <p className="pt-2">
                <strong>Hãy mô tả triệu chứng của thú cưng hoặc upload ảnh để bắt đầu!</strong>
              </p>
            </div>
          </div>

          {/* Helper tip bubble */}
          <div className="mt-5 flex w-full justify-end">
            <div className="max-w-[80%] rounded-2xl bg-amber-300 p-4 text-sm leading-relaxed text-amber-950 shadow-sm">
              Donec sit amet metus eget magna consectetur blandit. Duis non nisl
              commodo leo mollis aliquam. Maecenas at purus suscipit, fermentum
              arcu at, congue tortor.
            </div>
          </div>

          {/* Composer */}
          <div className="sticky bottom-0 mt-8 border-t bg-card pt-4">
            <form
              className="flex items-center gap-2 rounded-full border bg-background px-2 py-2"
              onSubmit={(e) => e.preventDefault()}
            >
              <Button
                type="button"
                variant="secondary"
                size="icon"
                className="h-9 w-9 shrink-0 rounded-full"
              >
                +
              </Button>
              <Input
                className="border-0 bg-transparent focus-visible:ring-0"
                placeholder="Mô tả triệu chứng thú cưng (vd: gãi tai...)"
              />
              <Button type="submit" size="icon" className="h-9 w-9 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="h-4 w-4"
                >
                  <path d="M4.5 12L3.2 4.9c-.2-1 .8-1.8 1.7-1.4l15.7 7.1c.9.4.9 1.7 0 2.1L4.9 19.8c-.9.4-1.9-.4-1.7-1.4L4.5 12zm0 0l6.7 0" />
                </svg>
              </Button>
            </form>
          </div>
        </div>

        {/* Small helper note */}
        <div className="mt-4 text-center text-xs text-muted-foreground">
          Lưu ý: Đây không thay thế chẩn đoán trực tiếp. Nếu tình trạng nặng, hãy đưa
          thú cưng đến bác sĩ ngay.
        </div>
      </section>
    </main>
  );
}


